import{a as ct,b as pt,c as dt}from"./chunk-AC42TJRX.js";import{a as lt}from"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{$,$a as Q,A as S,Ab as at,C as E,Cb as rt,D as v,Db as st,F as i,G as r,H as g,Ia as j,J as P,K as A,M as s,N as y,Na as U,O,Oa as G,Pa as V,Q as N,Qa as H,R as D,S as I,W as F,Wa as Y,X as z,Xa as K,Ya as J,ab as X,ea as R,fb as Z,g as T,ha as q,ka as B,mb as tt,nb as et,oa as W,p as M,ub as nt,vb as ot,y as m,zb as it}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{a as k,b as L,f as ut,h}from"./chunk-LNJ3S2LQ.js";var l=ut(dt());function gt(x,C){if(x&1&&(i(0,"div",31)(1,"ion-card")(2,"ion-card-content")(3,"div",32),g(4,"ion-icon",33),i(5,"span"),s(6,"Route to Nearest Center"),r()(),i(7,"div",34)(8,"div",35),g(9,"ion-icon",36),i(10,"span"),s(11),r()(),i(12,"div",35),g(13,"ion-icon",37),i(14,"span"),s(15),r()()()()()()),x&2){let _=A();m(9),v("name",_.travelMode==="walking"?"walk-outline":_.travelMode==="cycling"?"bicycle-outline":"car-outline"),m(2),O("",(_.routeTime/60).toFixed(0)," min"),m(4),O("",(_.routeDistance/1e3).toFixed(2)," km")}}var Tt=(()=>{let C=class C{constructor(){this.userMarker=null,this.routeLayer=null,this.nearestMarkers=[],this.evacuationCenters=[],this.centerCounts={earthquake:0,typhoon:0,flood:0,total:0},this.travelMode="walking",this.routeTime=0,this.routeDistance=0,this.userLocation=null,this.loadingCtrl=M(at),this.toastCtrl=M(rt),this.alertCtrl=M(it),this.http=M($),this.router=M(R),this.mapboxRouting=M(pt)}ngOnInit(){return h(this,null,function*(){console.log("\u{1F5FA}\uFE0F ALL MAPS: Initializing..."),yield this.loadAllMaps()})}loadAllMaps(){return h(this,null,function*(){let n=yield this.loadingCtrl.create({message:"Loading all evacuation centers...",spinner:"crescent"});yield n.present();try{let t=yield ct.getCurrentPosition({enableHighAccuracy:!0,timeout:2e4}),o=t.coords.latitude,e=t.coords.longitude;this.userLocation={lat:o,lng:e},console.log(`\u{1F5FA}\uFE0F ALL MAPS: User location [${o}, ${e}]`),this.initializeMap(o,e),yield this.loadAllCenters(o,e),yield n.dismiss(),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Showing all ${this.centerCounts.total} evacuation centers`,duration:3e3,color:"secondary",position:"top"})).present()}catch(t){yield n.dismiss(),console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading map",t),yield(yield this.alertCtrl.create({header:"Location Error",message:"Unable to get your location. Please enable GPS and try again.",buttons:[{text:"Retry",handler:()=>this.loadAllMaps()},{text:"Go Back",handler:()=>this.router.navigate(["/tabs/home"])}]})).present()}})}initializeMap(n,t){console.log(`\u{1F5FA}\uFE0F ALL MAPS: Initializing map at [${n}, ${t}]`),this.map&&this.map.remove(),this.map=l.map("all-maps").setView([n,t],12),l.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"OpenStreetMap contributors"}).addTo(this.map),this.userMarker=l.marker([n,t],{icon:l.icon({iconUrl:"assets/icons/user-location.png",iconSize:[30,30],iconAnchor:[15,30]})}).addTo(this.map),this.userMarker.bindPopup("\u{1F4CD} You are here!").openPopup()}loadAllCenters(n,t){return h(this,null,function*(){try{console.log("\u{1F5FA}\uFE0F ALL MAPS: Fetching all evacuation centers...");let o=yield T(this.http.get(`${lt.apiUrl}/evacuation-centers`));if(console.log("\u{1F5FA}\uFE0F ALL MAPS: Total centers received:",(o==null?void 0:o.length)||0),this.evacuationCenters=o||[],this.centerCounts.earthquake=this.evacuationCenters.filter(e=>e.disaster_type==="Earthquake").length,this.centerCounts.typhoon=this.evacuationCenters.filter(e=>e.disaster_type==="Typhoon").length,this.centerCounts.flood=this.evacuationCenters.filter(e=>e.disaster_type==="Flash Flood").length,this.centerCounts.total=this.evacuationCenters.length,console.log("\u{1F5FA}\uFE0F ALL MAPS: Center counts:",this.centerCounts),this.evacuationCenters.length===0){yield(yield this.alertCtrl.create({header:"No Evacuation Centers",message:"No evacuation centers found in the database.",buttons:["OK"]})).present();return}if(this.evacuationCenters.forEach(e=>{let a=Number(e.latitude),d=Number(e.longitude);if(!isNaN(a)&&!isNaN(d)){let c="assets/Location.png",p="\u26AA";switch(e.disaster_type){case"Earthquake":c="assets/forEarthquake.png",p="\u{1F7E0}";break;case"Typhoon":c="assets/forTyphoon.png",p="\u{1F7E2}";break;case"Flash Flood":c="assets/forFlood.png",p="\u{1F535}";break}let u=l.marker([a,d],{icon:l.icon({iconUrl:c,iconSize:[40,40],iconAnchor:[20,40],popupAnchor:[0,-40]})}),f=this.calculateDistance(n,t,a,d);u.on("click",()=>{this.showTransportationOptions(e)}),u.bindPopup(`
            <div class="evacuation-popup">
              <h3>${p} ${e.name}</h3>
              <p><strong>Type:</strong> ${e.disaster_type||"General"}</p>
              <p><strong>Distance:</strong> ${(f/1e3).toFixed(2)} km</p>
              <p><strong>Capacity:</strong> ${e.capacity||"N/A"}</p>
              <p><em>Click marker for route options</em></p>
            </div>
          `),u.addTo(this.map),console.log(`\u{1F5FA}\uFE0F Added ${e.disaster_type} marker: ${e.name}`)}}),this.evacuationCenters.length>0){let e=l.latLngBounds([]);e.extend([n,t]),this.evacuationCenters.forEach(a=>{e.extend([Number(a.latitude),Number(a.longitude)])}),this.map.fitBounds(e,{padding:[50,50]})}}catch(o){console.error("\u{1F5FA}\uFE0F ALL MAPS: Error loading centers",o),yield(yield this.toastCtrl.create({message:"Error loading evacuation centers. Please check your connection.",duration:3e3,color:"danger"})).present()}})}calculateDistance(n,t,o,e){let d=n*Math.PI/180,c=o*Math.PI/180,p=(o-n)*Math.PI/180,u=(e-t)*Math.PI/180,f=Math.sin(p/2)*Math.sin(p/2)+Math.cos(d)*Math.cos(c)*Math.sin(u/2)*Math.sin(u/2);return 6371e3*(2*Math.atan2(Math.sqrt(f),Math.sqrt(1-f)))}routeToTwoNearestCenters(){return h(this,null,function*(){if(!this.userLocation||this.evacuationCenters.length===0){console.log("\u{1F5FA}\uFE0F ALL MAPS: No user location or evacuation centers available");return}try{console.log("\u{1F5FA}\uFE0F ALL MAPS: Finding 2 nearest centers...");let n=this.getTwoNearestCenters(this.userLocation.lat,this.userLocation.lng);if(n.length===0){yield(yield this.toastCtrl.create({message:"No evacuation centers found nearby",duration:3e3,color:"warning"})).present();return}this.clearRoutes(),this.addPulsingMarkers(n),yield this.calculateRoutes(n),yield(yield this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Showing routes to ${n.length} nearest centers via ${this.travelMode}`,duration:3e3,color:"success",position:"top"})).present()}catch(n){console.error("\u{1F5FA}\uFE0F ALL MAPS: Error calculating routes",n),yield(yield this.toastCtrl.create({message:"Error calculating routes. Please try again.",duration:3e3,color:"danger"})).present()}})}getTwoNearestCenters(n,t){return this.evacuationCenters.map(e=>L(k({},e),{distance:this.calculateDistance(n,t,Number(e.latitude),Number(e.longitude))})).sort((e,a)=>e.distance-a.distance).slice(0,2)}addPulsingMarkers(n){n.forEach((t,o)=>{let e=Number(t.latitude),a=Number(t.longitude);if(!isNaN(e)&&!isNaN(a)){let d="assets/Location.png",c="#3880ff";t.disaster_type==="Earthquake"?(d="assets/forEarthquake.png",c="#ff9500"):t.disaster_type==="Typhoon"?(d="assets/forTyphoon.png",c="#2dd36f"):t.disaster_type==="Flash Flood"&&(d="assets/forFlood.png",c="#3dc2ff");let p=l.divIcon({className:"pulsing-marker",html:`
            <div class="pulse-container">
              <div class="pulse" style="background-color: ${c}"></div>
              <img src="${d}" class="marker-icon" />
              <div class="marker-label">${o+1}</div>
            </div>
          `,iconSize:[50,50],iconAnchor:[25,50]}),u=l.marker([e,a],{icon:p});u.bindPopup(`
          <div class="evacuation-popup nearest-popup">
            <h3>\u{1F3AF} Nearest Center #${o+1}</h3>
            <h4>${t.name}</h4>
            <p><strong>Type:</strong> ${t.disaster_type}</p>
            <p><strong>Distance:</strong> ${(t.distance/1e3).toFixed(2)} km</p>
            <p><strong>Capacity:</strong> ${t.capacity||"N/A"}</p>
          </div>
        `),u.addTo(this.map),this.nearestMarkers.push(u)}})}calculateRoutes(n){return h(this,null,function*(){if(this.userLocation){this.routeLayer=l.layerGroup().addTo(this.map);for(let t=0;t<n.length;t++){let o=n[t],e=Number(o.latitude),a=Number(o.longitude);if(!isNaN(e)&&!isNaN(a))try{let d=this.mapboxRouting.convertTravelModeToProfile(this.travelMode),c=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,a,e,d,{geometries:"geojson",overview:"simplified",steps:!1});if(c&&c.routes&&c.routes.length>0){let p=c.routes[0],u="#3880ff";o.disaster_type==="Earthquake"?u="#ff9500":o.disaster_type==="Typhoon"?u="#2dd36f":o.disaster_type==="Flash Flood"&&(u="#3dc2ff"),l.polyline(p.geometry.coordinates.map(b=>[b[1],b[0]]),{color:u,weight:4,opacity:.8,dashArray:t===0?void 0:"10, 10"}).addTo(this.routeLayer),t===0&&(this.routeTime=p.duration,this.routeDistance=p.distance),console.log(`\u{1F5FA}\uFE0F Route ${t+1}: ${(p.distance/1e3).toFixed(2)}km, ${(p.duration/60).toFixed(0)}min`)}}catch(d){console.error(`\u{1F5FA}\uFE0F Error calculating route to center ${t+1}:`,d)}}}})}clearRoutes(){this.routeLayer&&(this.map.removeLayer(this.routeLayer),this.routeLayer=null),this.nearestMarkers.forEach(n=>{this.map.removeLayer(n)}),this.nearestMarkers=[],this.routeTime=0,this.routeDistance=0}onTravelModeChange(n){let t=n.detail.value;(t==="walking"||t==="cycling"||t==="driving")&&this.changeTravelMode(t)}changeTravelMode(n){return h(this,null,function*(){this.travelMode=n,yield(yield this.toastCtrl.create({message:`\u{1F6B6}\u200D\u2642\uFE0F Travel mode changed to ${n}`,duration:2e3,color:"primary"})).present(),this.userLocation&&this.evacuationCenters.length>0&&(yield this.routeToTwoNearestCenters())})}showTransportationOptions(n){return h(this,null,function*(){yield(yield this.alertCtrl.create({header:`Route to ${n.name}`,message:"Choose your transportation mode:",buttons:[{text:"\u{1F6B6}\u200D\u2642\uFE0F Walk",handler:()=>{this.routeToCenter(n,"walking")}},{text:"\u{1F6B4}\u200D\u2642\uFE0F Cycle",handler:()=>{this.routeToCenter(n,"cycling")}},{text:"\u{1F697} Drive",handler:()=>{this.routeToCenter(n,"driving")}},{text:"Cancel",role:"cancel"}]})).present()})}routeToCenter(n,t){return h(this,null,function*(){if(this.userLocation)try{this.clearRoutes();let o=Number(n.latitude),e=Number(n.longitude);if(!isNaN(o)&&!isNaN(e)){let a=this.mapboxRouting.convertTravelModeToProfile(t),d=yield this.mapboxRouting.getDirections(this.userLocation.lng,this.userLocation.lat,e,o,a,{geometries:"geojson",overview:"full",steps:!1});if(d&&d.routes&&d.routes.length>0){let c=d.routes[0],p="#3880ff",u="\u{1F535}";n.disaster_type==="Earthquake"?(p="#ff9500",u="\u{1F7E0}"):n.disaster_type==="Typhoon"?(p="#2dd36f",u="\u{1F7E2}"):n.disaster_type==="Flash Flood"&&(p="#3dc2ff",u="\u{1F535}"),this.routeLayer=l.layerGroup().addTo(this.map);let f=l.polyline(c.geometry.coordinates.map(w=>[w[1],w[0]]),{color:p,weight:5,opacity:.8});f.addTo(this.routeLayer),yield(yield this.toastCtrl.create({message:`${u} Route: ${(c.distance/1e3).toFixed(2)}km, ${(c.duration/60).toFixed(0)}min via ${t}`,duration:4e3,color:"primary"})).present(),this.map.fitBounds(f.getBounds(),{padding:[50,50]})}}}catch(o){console.error("\u{1F5FA}\uFE0F Error routing to center:",o),yield(yield this.toastCtrl.create({message:"Error calculating route. Please try again.",duration:3e3,color:"danger"})).present()}})}goBack(){this.router.navigate(["/tabs/home"])}ionViewWillLeave(){this.clearRoutes(),this.map&&this.map.remove()}};C.\u0275fac=function(t){return new(t||C)},C.\u0275cmp=S({type:C,selectors:[["app-all-maps"]],decls:66,vars:8,consts:[[3,"translucent"],["color","secondary"],["slot","start"],[3,"click"],["name","chevron-back-outline"],[3,"fullscreen"],["id","all-maps",2,"height","100%","width","100%"],[1,"floating-info"],[1,"info-header"],["name","map","color","secondary"],[1,"disaster-counts"],[1,"count-row"],[1,"disaster-icon"],[1,"disaster-label"],[1,"disaster-count"],[1,"info-text"],[1,"transport-controls"],[1,"transport-header"],["name","navigate-outline","color","primary"],[3,"ngModelChange","ionChange","ngModel"],["value","walking"],["name","walk-outline"],["value","cycling"],["name","bicycle-outline"],["value","driving"],["name","car-outline"],["class","route-info",4,"ngIf"],["vertical","bottom","horizontal","end","slot","fixed"],["color","primary",3,"click"],["name","navigate-outline"],[1,"fab-label"],[1,"route-info"],[1,"route-header"],["name","time-outline","color","success"],[1,"route-details"],[1,"route-item"],[3,"name"],["name","location-outline"]],template:function(t,o){t&1&&(i(0,"ion-header",0)(1,"ion-toolbar",1)(2,"ion-buttons",2)(3,"ion-button",3),P("click",function(){return o.goBack()}),g(4,"ion-icon",4),r()(),i(5,"ion-title"),s(6,"\u{1F5FA}\uFE0F All Evacuation Centers"),r()()(),i(7,"ion-content",5),g(8,"div",6),i(9,"div",7)(10,"ion-card")(11,"ion-card-content")(12,"div",8),g(13,"ion-icon",9),i(14,"span"),s(15),r()(),i(16,"div",10)(17,"div",11)(18,"span",12),s(19,"\u{1F7E0}"),r(),i(20,"span",13),s(21,"Earthquake:"),r(),i(22,"span",14),s(23),r()(),i(24,"div",11)(25,"span",12),s(26,"\u{1F7E2}"),r(),i(27,"span",13),s(28,"Typhoon:"),r(),i(29,"span",14),s(30),r()(),i(31,"div",11)(32,"span",12),s(33,"\u{1F535}"),r(),i(34,"span",13),s(35,"Flood:"),r(),i(36,"span",14),s(37),r()()(),i(38,"div",15),s(39," Complete overview of all evacuation centers by disaster type "),r()()()(),i(40,"div",16)(41,"ion-card")(42,"ion-card-content")(43,"div",17),g(44,"ion-icon",18),i(45,"span"),s(46,"Travel Mode"),r()(),i(47,"ion-segment",19),I("ngModelChange",function(a){return D(o.travelMode,a)||(o.travelMode=a),a}),P("ionChange",function(a){return o.onTravelModeChange(a)}),i(48,"ion-segment-button",20),g(49,"ion-icon",21),i(50,"ion-label"),s(51,"Walk"),r()(),i(52,"ion-segment-button",22),g(53,"ion-icon",23),i(54,"ion-label"),s(55,"Bike"),r()(),i(56,"ion-segment-button",24),g(57,"ion-icon",25),i(58,"ion-label"),s(59,"Drive"),r()()()()()(),E(60,gt,16,3,"div",26),i(61,"ion-fab",27)(62,"ion-fab-button",28),P("click",function(){return o.routeToTwoNearestCenters()}),g(63,"ion-icon",29),r(),i(64,"ion-label",30),s(65,"Route to 2 Nearest Centers"),r()()()),t&2&&(v("translucent",!0),m(7),v("fullscreen",!0),m(8),O("All Centers: ",o.centerCounts.total,""),m(8),y(o.centerCounts.earthquake),m(7),y(o.centerCounts.typhoon),m(7),y(o.centerCounts.flood),m(10),N("ngModel",o.travelMode),m(13),v("ngIf",o.routeTime&&o.routeDistance))},dependencies:[st,U,G,V,H,Y,K,J,Q,X,Z,tt,et,nt,ot,j,z,F,W,q,B],styles:["#all-maps[_ngcontent-%COMP%]{height:100%;width:100%;z-index:1}.floating-info[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;z-index:1000;max-width:280px}.floating-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.floating-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.floating-info[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-secondary);margin-bottom:8px}.floating-info[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]{margin:8px 0}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;margin:4px 0;font-size:13px}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-icon[_ngcontent-%COMP%]{font-size:14px;width:16px;text-align:center}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-label[_ngcontent-%COMP%]{flex:1;color:var(--ion-color-dark)}.floating-info[_ngcontent-%COMP%]   .disaster-counts[_ngcontent-%COMP%]   .count-row[_ngcontent-%COMP%]   .disaster-count[_ngcontent-%COMP%]{font-weight:600;color:var(--ion-color-secondary);min-width:20px;text-align:right}.floating-info[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{font-size:11px;color:var(--ion-color-medium);line-height:1.3;margin-top:8px;padding-top:8px;border-top:1px solid var(--ion-color-light)}ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-secondary);--color: white}ion-title[_ngcontent-%COMP%]{font-weight:600}.transport-controls[_ngcontent-%COMP%]{position:absolute;bottom:120px;left:20px;z-index:1000;max-width:280px}.transport-controls[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.transport-controls[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-primary);margin-bottom:8px}.transport-controls[_ngcontent-%COMP%]   .transport-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.transport-controls[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{--background: rgba(var(--ion-color-light-rgb), .3);border-radius:8px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{--color: var(--ion-color-medium);--color-checked: var(--ion-color-primary);--indicator-color: var(--ion-color-primary);min-height:40px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:2px}.transport-controls[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.route-info[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;z-index:1000;max-width:200px}.route-info[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{margin:0;box-shadow:0 4px 8px #0003;border-radius:12px;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.route-info[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:12px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-weight:600;color:var(--ion-color-success);margin-bottom:8px}.route-info[_ngcontent-%COMP%]   .route-header[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.route-info[_ngcontent-%COMP%]   .route-details[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:14px;color:var(--ion-color-dark)}.route-info[_ngcontent-%COMP%]   .route-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px;color:var(--ion-color-primary)}.fab-label[_ngcontent-%COMP%]{position:absolute;right:60px;top:50%;transform:translateY(-50%);background:#000000b3;color:#fff;padding:4px 8px;border-radius:4px;font-size:12px;white-space:nowrap;pointer-events:none}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse-container[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center}[_ngcontent-%COMP%]:global(.pulsing-marker)   .pulse[_ngcontent-%COMP%]{position:absolute;width:60px;height:60px;border-radius:50%;opacity:.6;animation:_ngcontent-%COMP%_pulse 2s infinite;z-index:1}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-icon[_ngcontent-%COMP%]{width:40px;height:40px;z-index:2;position:relative}[_ngcontent-%COMP%]:global(.pulsing-marker)   .marker-label[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;background:var(--ion-color-primary);color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;z-index:3;border:2px solid white}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(.8);opacity:.8}50%{transform:scale(1.2);opacity:.4}to{transform:scale(.8);opacity:.8}}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]{text-align:center;min-width:200px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:var(--ion-color-secondary);font-size:16px;font-weight:600}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:4px 0;color:var(--ion-color-dark);font-size:14px;font-weight:500}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-size:14px}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:var(--ion-color-dark)}[_ngcontent-%COMP%]:global(.leaflet-popup-content)   .evacuation-popup.nearest-popup[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-success);font-size:18px}"]});let x=C;return x})();export{Tt as AllMapsPage};
