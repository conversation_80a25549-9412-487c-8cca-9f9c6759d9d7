#flood-map {
  height: 100%;
  width: 100%;
  z-index: 1;
}

.floating-info {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 250px;

  ion-card {
    margin: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  ion-card-content {
    padding: 12px;
  }

  .info-row {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--ion-color-primary);
    margin-bottom: 4px;

    ion-icon {
      font-size: 18px;
    }
  }

  .info-text {
    font-size: 12px;
    color: var(--ion-color-medium);
    line-height: 1.3;
  }
}

// Flood-specific styling
ion-toolbar {
  --background: var(--ion-color-primary);
  --color: white;
}

ion-title {
  font-weight: 600;
}

// Download button styling
ion-buttons[slot="end"] {
  ion-button {
    --color: white;
    --background: rgba(255, 255, 255, 0.1);
    --border-radius: 8px;
    margin-right: 8px;

    &:hover {
      --background: rgba(255, 255, 255, 0.2);
    }

    &[disabled] {
      --color: rgba(255, 255, 255, 0.5);
      --background: rgba(255, 255, 255, 0.05);
    }

    ion-icon {
      font-size: 20px;
    }
  }
}

// Popup styling for flood centers
:global(.leaflet-popup-content) {
  .evacuation-popup {
    text-align: center;
    min-width: 200px;

    h3 {
      margin: 0 0 8px 0;
      color: var(--ion-color-primary);
      font-size: 16px;
      font-weight: 600;
    }

    p {
      margin: 4px 0;
      font-size: 14px;
      
      strong {
        color: var(--ion-color-dark);
      }
    }
  }
}
