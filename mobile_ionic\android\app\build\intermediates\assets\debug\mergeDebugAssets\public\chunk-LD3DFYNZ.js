import{a as j}from"./chunk-FULEFYAM.js";import{$ as G,$a as S,A as f,Bb as x,C as T,Cb as L,D,Db as C,F as e,G as n,H as s,J as m,M as t,N as u,Na as y,O as F,Oa as _,V as z,Wa as h,X as v,ab as I,cb as P,ea as A,fb as E,ga as B,gb as O,oa as w,ub as M,vb as b,y as g,z as p,zb as N}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as d}from"./chunk-LNJ3S2LQ.js";var U=".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}.modal-section-title[_ngcontent-%COMP%]{font-size:.9375rem;margin-bottom:15px}";function W(r,o){if(r&1&&(e(0,"div",7)(1,"div",8)(2,"span",9),t(3),n(),e(4,"span",10),t(5),n()()()),r&2){let k=o.$implicit;g(3),u(k.icon),g(2),u(k.label)}}var le=(()=>{let o=class o{constructor(i,a,l,c,H){this.modalCtrl=i,this.alertCtrl=a,this.toastCtrl=l,this.http=c,this.router=H,this.userData={},this.loadUserData()}goToSettings(){this.router.navigate(["/settings"])}loadUserData(){let i=localStorage.getItem("userData");i&&(this.userData=JSON.parse(i))}openTermsModal(){return d(this,null,function*(){yield(yield this.modalCtrl.create({component:Y,cssClass:"terms-modal"})).present()})}openPrivacyModal(){return d(this,null,function*(){yield(yield this.modalCtrl.create({component:R,cssClass:"terms-modal"})).present()})}openEmergencyContactsModal(){return d(this,null,function*(){yield(yield this.modalCtrl.create({component:$,cssClass:"terms-modal"})).present()})}openSafetyTipsModal(){return d(this,null,function*(){yield(yield this.modalCtrl.create({component:J,cssClass:"terms-modal"})).present()})}openGuideModal(){return d(this,null,function*(){yield(yield this.modalCtrl.create({component:q,cssClass:"terms-modal"})).present()})}openAccountInfoModal(){return d(this,null,function*(){yield(yield this.modalCtrl.create({component:K,cssClass:"account-info-modal"})).present()})}testFCM(){return d(this,null,function*(){if(localStorage.getItem("google_play_services_missing")==="true"){yield(yield this.alertCtrl.create({header:"Google Play Services Required",message:"Push notifications require Google Play Services. Would you like to install or update Google Play Services?",buttons:[{text:"Install/Update",handler:()=>{window.open("market://details?id=com.google.android.gms","_system")}},{text:"Continue Anyway",handler:()=>{this.checkFCMToken()}}]})).present();return}yield this.checkFCMToken()})}checkFCMToken(){return d(this,null,function*(){let i=localStorage.getItem("fcm_token");if(!i){yield(yield this.alertCtrl.create({header:"No FCM Token",message:"No FCM token found. Please restart the app to generate a token.",buttons:["OK"]})).present();return}yield(yield this.alertCtrl.create({header:"FCM Token",message:`Current token: ${i.substring(0,20)}...`,buttons:[{text:"Test Local Notification",handler:()=>{this.showTestNotification()}},{text:"Send from Backend",handler:()=>{this.sendTestNotificationFromBackend(i)}},{text:"Check Google Play",handler:()=>{this.checkGooglePlayServices()}},{text:"Cancel",role:"cancel"}]})).present()})}checkGooglePlayServices(){return d(this,null,function*(){try{window.open("market://details?id=com.google.android.gms","_system")}catch(i){console.error("Error opening Google Play Store:",i),yield(yield this.alertCtrl.create({header:"Error",message:"Could not open Google Play Store. Please check if Google Play Store is installed on your device.",buttons:["OK"]})).present()}})}showTestNotification(){return d(this,null,function*(){let i={title:"Test Notification",body:"This is a local test notification",category:"General",severity:"medium",wasTapped:!1,time:new Date().toISOString()};"vibrate"in navigator&&navigator.vibrate([500,100,500]),yield(yield this.alertCtrl.create({header:i.title,subHeader:i.category?`${i.category.toUpperCase()}`:"",message:i.body,buttons:["OK"]})).present()})}sendTestNotificationFromBackend(i){return d(this,null,function*(){yield(yield this.toastCtrl.create({message:"Sending test notification from backend...",duration:2e3})).present(),this.http.post(`${j.apiUrl}/test-notification`,{token:i,title:"Test from App",message:"This is a test notification sent from the app",category:"General",severity:"medium"}).subscribe({next:()=>{this.toastCtrl.create({message:"Test notification sent successfully!",duration:3e3,color:"success"}).then(l=>l.present())},error:l=>{this.alertCtrl.create({header:"Error",message:`Failed to send test notification: ${l.message||JSON.stringify(l)}`,buttons:["OK"]}).then(c=>c.present())}})})}};o.\u0275fac=function(a){return new(a||o)(p(x),p(N),p(L),p(G),p(A))},o.\u0275cmp=f({type:o,selectors:[["app-profile"]],decls:36,vars:2,consts:[[1,"profile-section","ion-padding",2,"color","rgb(255, 255, 255)","background-color","rgb(3, 178, 221)","height","200px"],[1,"profile-info",2,"margin-top","115px","margin-right","260px"],[2,"color","black"],["lines","full",2,"margin-top","50px"],["button","",2,"padding-top","10px",3,"click"],["src","assets/setting (1).png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],[2,"padding-left","15px","font-size","17px"],["src","assets/info.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/medical-call.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/first-aid-box.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/shield.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/terms-and-conditions.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["name","settings-outline","slot","start",2,"width","28px","height","28px","display","block","margin","auto","color","#3880ff"]],template:function(a,l){a&1&&(e(0,"ion-content")(1,"div",0)(2,"div",1)(3,"h2"),t(4),n(),e(5,"p",2),t(6),n()()(),e(7,"ion-list",3)(8,"ion-item",4),m("click",function(){return l.openAccountInfoModal()}),s(9,"img",5),e(10,"ion-label",6),t(11,"Account Information"),n()(),e(12,"ion-item",4),m("click",function(){return l.openGuideModal()}),s(13,"img",7),e(14,"ion-label",6),t(15,"Reference Guide for Map Symbols"),n()(),e(16,"ion-item",4),m("click",function(){return l.openEmergencyContactsModal()}),s(17,"img",8),e(18,"ion-label",6),t(19,"Emergency Contacts"),n()(),e(20,"ion-item",4),m("click",function(){return l.openSafetyTipsModal()}),s(21,"img",9),e(22,"ion-label",6),t(23,"Safety Tips"),n()(),e(24,"ion-item",4),m("click",function(){return l.openPrivacyModal()}),s(25,"img",10),e(26,"ion-label",6),t(27,"Privacy Policy"),n()(),e(28,"ion-item",4),m("click",function(){return l.openTermsModal()}),s(29,"img",11),e(30,"ion-label",6),t(31,"Terms and Condition"),n()(),e(32,"ion-item",4),m("click",function(){return l.goToSettings()}),s(33,"ion-icon",12),e(34,"ion-label",6),t(35,"Notification Settings"),n()()()()),a&2&&(g(4),F("Hi, ",l.userData.full_name,""),g(2),u(l.userData.email))},dependencies:[C,h,I,P,E,O,v,w,B],styles:['@charset "UTF-8";ion-header[_ngcontent-%COMP%]{background:var(--ion-color-primary)}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#fff}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-back-button[_ngcontent-%COMP%]{--color: white}.profile-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-align:center;gap:16px;margin-bottom:16px;padding:24px 16px;background:#fff}.profile-section[_ngcontent-%COMP%]   ion-avatar[_ngcontent-%COMP%]{width:60px;height:60px}.profile-section[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:18px;font-weight:500}.profile-section[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0 0;color:var(--ion-color-medium);font-size:14px}ion-list[_ngcontent-%COMP%]{background:transparent}ion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--padding-end: 16px;--min-height: 56px;margin-bottom:1px}ion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:15px;margin-right:16px;color:var(--ion-color-medium)}ion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:16px;font-weight:400}.terms-modal[_ngcontent-%COMP%]{--height: 90%;--border-radius: 16px}.terms-modal[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-light)}.terms-modal[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:24px;font-weight:700;margin-bottom:8px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   .effective-date[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:14px;margin-bottom:24px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:18px;font-weight:600;margin:24px 0 12px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:16px;line-height:1.5;margin-bottom:16px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:16px;line-height:1.5;margin-bottom:8px;padding-left:24px;position:relative}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:"\\2022";position:absolute;left:8px;color:var(--ion-color-primary)}.terms-modal[_ngcontent-%COMP%]   .legend-title[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:20px;font-weight:600;margin-bottom:24px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]{margin-top:30px;display:flex;flex-direction:column;align-items:center}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;background:var(--ion-color-light);border-radius:8px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:32px;height:32px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   .legend-icon[_ngcontent-%COMP%]{font-size:20px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .legend-label[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:16px}']});let r=o;return r})(),Y=(()=>{let o=class o{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}};o.\u0275fac=function(a){return new(a||o)(p(x))},o.\u0275cmp=f({type:o,selectors:[["ng-component"]],decls:61,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"terms-content"],[1,"modal-section-title"],[1,"effective-date"],[1,"welcome"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0)(3,"strong"),t(4,"Terms and Conditions"),n()(),e(5,"ion-buttons",1)(6,"ion-button",2),m("click",function(){return l.dismiss()}),t(7,"Close"),n()()()(),e(8,"ion-content",3)(9,"div",4)(10,"h1",5)(11,"strong"),t(12,"Terms and Conditions"),n()(),e(13,"p",6),t(14,"Effective Date: April 26, 2025"),n(),e(15,"p",7),t(16,'Welcome to Evacuation Mapping System ("we", "our", or "us"). These '),e(17,"strong"),t(18,"Terms and Conditions"),n(),t(19,' ("Terms") govern your access to and use of our online evacuation mapping system (the "Service"). By registering or using the Service, you agree to be bound by these Terms.'),n(),e(20,"section")(21,"h2",5),t(22,"1. User Eligibility"),n(),e(23,"p"),t(24,"To use this service, you must be at least 13 years old. By registering, you confirm that the information provided is accurate and complete."),n()(),e(25,"section")(26,"h2",5),t(27,"2. User Account"),n(),e(28,"p"),t(29,"To access certain features of the Service, you must create an account. You agree to provide:"),n(),e(30,"ul")(31,"li"),t(32,"Your full name"),n(),e(33,"li"),t(34,"A valid email address"),n(),e(35,"li"),t(36,"A password"),n(),e(37,"li"),t(38,"Your location data (for accurate evacuation mapping)"),n()(),e(39,"p"),t(40,"You are responsible for maintaining the confidentiality of your account and for all activities that occur under your account."),n()(),e(41,"section")(42,"h2",5),t(43,"3. Use of Service"),n(),e(44,"p"),t(45,"You agree to use the system solely for lawful purposes and in a way that does not infringe the rights of others. Misuse of the system, including providing false information or tampering with the mapping process, may result in suspension or termination of your account."),n()(),e(46,"section")(47,"h2",5),t(48,"4. Modifications"),n(),e(49,"p"),t(50,"We reserve the right to modify or discontinue the Service at any time without notice. Continued use of the Service following changes means you accept those changes."),n()(),e(51,"section")(52,"h2",5),t(53,"5. Limitation of Liability"),n(),e(54,"p"),t(55,"We strive to provide accurate evacuation data but do not guarantee the completeness, accuracy, or timeliness of the information provided. We are not liable for any loss or damage arising from the use or inability to use the Service."),n()(),e(56,"section")(57,"h2",5),t(58,"6. Termination"),n(),e(59,"p"),t(60,"We may suspend or terminate your access to the Service if you violate these Terms."),n()()()())},dependencies:[C,y,_,h,S,M,b],styles:[U]});let r=o;return r})(),R=(()=>{let o=class o{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}};o.\u0275fac=function(a){return new(a||o)(p(x))},o.\u0275cmp=f({type:o,selectors:[["ng-component"]],decls:66,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"modal-section-title"],[1,"effective-date"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0)(3,"strong"),t(4,"Privacy Policy"),n()(),e(5,"ion-buttons",1)(6,"ion-button",2),m("click",function(){return l.dismiss()}),t(7,"Close"),n()()()(),e(8,"ion-content",3)(9,"h2",4)(10,"strong"),t(11,"Privacy Policy"),n()(),e(12,"p",5),t(13,"Effective Date: April 26, 2025"),n(),e(14,"p"),t(15,"DisasterGuard is committed to protecting your privacy. This "),e(16,"strong"),t(17,"Privacy Policy"),n(),t(18," outlines how we collect, use, and protect your information when you use our evacuation mapping system."),n(),e(19,"h3",4),t(20,"1. Information We Collect"),n(),e(21,"p"),t(22,"We collect the following personal information upon registration:"),n(),e(23,"ul")(24,"li"),t(25,"Name"),n(),e(26,"li"),t(27,"Email address"),n(),e(28,"li"),t(29,"Password (stored securely)"),n(),e(30,"li"),t(31,"Location data (for evacuation mapping purposes)"),n()(),e(32,"h3",4),t(33,"2. How We Use Your Information"),n(),e(34,"p"),t(35,"Your data is used solely to:"),n(),e(36,"ul")(37,"li"),t(38,"Provide personalized evacuation routes and mapping"),n(),e(39,"li"),t(40,"Contact you regarding urgent updates or emergencies"),n(),e(41,"li"),t(42,"Improve system functionality"),n()(),e(43,"p"),t(44,"We do not sell, rent, or share your personal information with third parties, except as required by law or to ensure user safety during emergencies."),n(),e(45,"h3",4),t(46,"3. Data Security"),n(),e(47,"p"),t(48,"We implement appropriate security measures to protect your data. Your password is encrypted, and location data is only used to provide real-time evacuation support."),n(),e(49,"h3",4),t(50,"4. Your Rights"),n(),e(51,"p"),t(52,"You may:"),n(),e(53,"ul")(54,"li"),t(55,"Access or update your personal data"),n(),e(56,"li"),t(57,"Request deletion of your account"),n(),e(58,"li"),t(59,"Opt-out of communications at any time"),n()(),e(60,"p"),t(61,"To do so, contact us at: <EMAIL>"),n(),e(62,"h3",4),t(63,"5. Changes to This Policy"),n(),e(64,"p"),t(65,"We may update this Privacy Policy occasionally. You will be notified of any significant changes."),n()())},dependencies:[C,y,_,h,S,M,b],styles:[U]});let r=o;return r})(),q=(()=>{let o=class o{constructor(i){this.modalCtrl=i,this.legendItems=[{icon:"\u{1F7E2}",label:"Your Location"},{icon:"\u{1F7E1}",label:"for Earthquake"},{icon:"\u26AB",label:"for Typhoon"},{icon:"\u{1F535}",label:"for Flash flood"}]}dismiss(){this.modalCtrl.dismiss()}};o.\u0275fac=function(a){return new(a||o)(p(x))},o.\u0275cmp=f({type:o,selectors:[["ng-component"]],decls:13,vars:1,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"modal-section-title"],[1,"legend-items"],["class","legend-item",4,"ngFor","ngForOf"],[1,"legend-item"],[1,"legend-icon-container"],[1,"legend-icon"],[1,"legend-label"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0),t(3,"Map Symbols Guide"),n(),e(4,"ion-buttons",1)(5,"ion-button",2),m("click",function(){return l.dismiss()}),t(6,"Close"),n()()()(),e(7,"ion-content",3)(8,"h3",4)(9,"strong"),t(10,"Reference Guide for Map Symbols"),n()(),e(11,"div",5),T(12,W,6,2,"div",6),n()()),a&2&&(g(12),D("ngForOf",l.legendItems))},dependencies:[C,y,_,h,S,M,b,v,z],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}.modal-section-title[_ngcontent-%COMP%]{font-size:.9375rem;margin-bottom:15px}.legend-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px}.legend-icon-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:15px}.legend-icon[_ngcontent-%COMP%]{font-size:24px;width:30px;text-align:center}.legend-label[_ngcontent-%COMP%]{flex-grow:1}"]});let r=o;return r})(),K=(()=>{let o=class o{constructor(i){this.modalCtrl=i,this.userData={},this.loadUserData()}loadUserData(){let i=localStorage.getItem("userData");i&&(this.userData=JSON.parse(i))}dismiss(){this.modalCtrl.dismiss()}};o.\u0275fac=function(a){return new(a||o)(p(x))},o.\u0275cmp=f({type:o,selectors:[["ng-component"]],decls:44,vars:5,consts:[["slot","end"],[3,"click"],[1,"ion-padding"],["name","person-outline","slot","start"],["name","call-outline","slot","start"],["name","calendar-outline","slot","start"],["name","male-female-outline","slot","start"],["name","location-outline","slot","start"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title"),t(3,"Account Information"),n(),e(4,"ion-buttons",0)(5,"ion-button",1),m("click",function(){return l.dismiss()}),t(6,"Close"),n()()()(),e(7,"ion-content",2)(8,"ion-list")(9,"ion-item"),s(10,"ion-icon",3),e(11,"ion-label")(12,"h2"),t(13,"Full Name"),n(),e(14,"p"),t(15),n()()(),e(16,"ion-item"),s(17,"ion-icon",4),e(18,"ion-label")(19,"h2"),t(20,"Contact Number"),n(),e(21,"p"),t(22),n()()(),e(23,"ion-item"),s(24,"ion-icon",5),e(25,"ion-label")(26,"h2"),t(27,"Age"),n(),e(28,"p"),t(29),n()()(),e(30,"ion-item"),s(31,"ion-icon",6),e(32,"ion-label")(33,"h2"),t(34,"Gender"),n(),e(35,"p"),t(36),n()()(),e(37,"ion-item"),s(38,"ion-icon",7),e(39,"ion-label")(40,"h2"),t(41,"Address"),n(),e(42,"p"),t(43),n()()()()()),a&2&&(g(15),u(l.userData.full_name),g(7),u(l.userData.mobile_number),g(7),u(l.userData.age),g(7),u(l.userData.gender),g(7),u(l.userData.address))},dependencies:[C,y,_,h,S,I,P,E,O,M,b,v,w],encapsulation:2});let r=o;return r})(),$=(()=>{let o=class o{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}};o.\u0275fac=function(a){return new(a||o)(p(x))},o.\u0275cmp=f({type:o,selectors:[["ng-component"]],decls:44,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],["name","call-outline","slot","start"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0),t(3,"Emergency Contacts"),n(),e(4,"ion-buttons",1)(5,"ion-button",2),m("click",function(){return l.dismiss()}),t(6,"Close"),n()()()(),e(7,"ion-content",3)(8,"ion-list")(9,"ion-item"),s(10,"ion-icon",4),e(11,"ion-label")(12,"h2"),t(13,"National Emergency Hotline"),n(),e(14,"p"),t(15,"911"),n()()(),e(16,"ion-item"),s(17,"ion-icon",4),e(18,"ion-label")(19,"h2"),t(20,"Fire Department"),n(),e(21,"p"),t(22,"160"),n()()(),e(23,"ion-item"),s(24,"ion-icon",4),e(25,"ion-label")(26,"h2"),t(27,"Police"),n(),e(28,"p"),t(29,"117"),n()()(),e(30,"ion-item"),s(31,"ion-icon",4),e(32,"ion-label")(33,"h2"),t(34,"Red Cross"),n(),e(35,"p"),t(36,"143"),n()()(),e(37,"ion-item"),s(38,"ion-icon",4),e(39,"ion-label")(40,"h2"),t(41,"Local Disaster Office"),n(),e(42,"p"),t(43,"Contact your LGU"),n()()()()())},dependencies:[C,y,_,h,S,I,P,E,O,M,b,v],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}h2[_ngcontent-%COMP%]{font-size:1rem;margin-bottom:4px}p[_ngcontent-%COMP%]{font-size:.95rem;color:var(--ion-color-medium)}"]});let r=o;return r})(),J=(()=>{let o=class o{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}};o.\u0275fac=function(a){return new(a||o)(p(x))},o.\u0275cmp=f({type:o,selectors:[["ng-component"]],decls:49,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0),t(3,"Safety Tips"),n(),e(4,"ion-buttons",1)(5,"ion-button",2),m("click",function(){return l.dismiss()}),t(6,"Close"),n()()()(),e(7,"ion-content",3)(8,"ion-list")(9,"ion-item")(10,"ion-label")(11,"h2"),t(12,"Earthquake"),n(),e(13,"ul")(14,"li"),t(15,"Drop, Cover, and Hold On."),n(),e(16,"li"),t(17,"Stay away from windows and heavy objects."),n(),e(18,"li"),t(19,"Evacuate only when safe."),n()()()(),e(20,"ion-item")(21,"ion-label")(22,"h2"),t(23,"Flood"),n(),e(24,"ul")(25,"li"),t(26,"Move to higher ground immediately."),n(),e(27,"li"),t(28,"Avoid walking or driving through floodwaters."),n()()()(),e(29,"ion-item")(30,"ion-label")(31,"h2"),t(32,"Typhoon"),n(),e(33,"ul")(34,"li"),t(35,"Stay indoors and away from glass windows."),n(),e(36,"li"),t(37,"Prepare an emergency kit."),n()()()(),e(38,"ion-item")(39,"ion-label")(40,"h2"),t(41,"General"),n(),e(42,"ul")(43,"li"),t(44,"Keep emergency contacts accessible."),n(),e(45,"li"),t(46,"Prepare a Go Bag with essentials."),n(),e(47,"li"),t(48,"Stay informed via official channels."),n()()()()()())},dependencies:[C,y,_,h,S,P,E,O,M,b,v],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}h2[_ngcontent-%COMP%]{font-size:1rem;margin-bottom:4px}ul[_ngcontent-%COMP%]{margin:0;padding-left:18px;font-size:.95rem;color:var(--ion-color-medium)}li[_ngcontent-%COMP%]{margin-bottom:4px}"]});let r=o;return r})();export{K as AccountInfoModalComponent,$ as EmergencyContactsModalComponent,q as GuideModalComponent,R as PrivacyModalComponent,le as ProfilePage,J as SafetyTipsModalComponent,Y as TermsModalComponent};
