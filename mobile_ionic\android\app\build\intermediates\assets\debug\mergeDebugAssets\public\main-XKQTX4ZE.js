import{a as n1,b as l1}from"./chunk-3KLEUTE3.js";import{a as i1}from"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{A as N,B as f,Da as K,Db as t1,Ea as _,F as W,G,H as $,La as o1,Y as X,Z as Q,a as P,aa as C,ba as J,d as F,da as Y,ea as k,f as I,fa as Z,ga as A,i as D,k as R,l as U,m as w,n as m,o as u,p as M,t as T,v as E,wb as a1,z,zb as s1}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{h as q}from"./chunk-LNJ3S2LQ.js";function c1(){if(typeof process>"u"){var a=typeof window<"u"?window:{},o=5e3,i=Date.now(),s=!1;a.document.addEventListener("deviceready",function(){console.log("Ionic Native: deviceready event fired after "+(Date.now()-i)+" ms"),s=!0}),setTimeout(function(){!s&&a.cordova&&console.warn("Ionic Native: deviceready did not fire within "+o+"ms. This can happen when plugins are in an inconsistent state. Try removing plugins from plugins/ and reinstalling them.")},o)}}var i2={error:"cordova_not_available"},n2={error:"plugin_not_installed"};function y(a){var o=function(){if(Promise)return new Promise(function(n,l){a(n,l)});console.error("No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.")};if(typeof window<"u"&&window.angular){var i=window.document,s=window.angular.element(i.querySelector("[ng-app]")||i.body).injector();if(s){var t=s.get("$q");return t(function(n,l){a(n,l)})}console.warn("Angular 1 was detected but $q couldn't be retrieved. This is usually when the app is not bootstrapped on the html or body tag. Falling back to native promises which won't trigger an automatic digest when promises resolve.")}return o()}function l2(a,o,i,s){s===void 0&&(s={});var t,n,l=y(function(e,p){s.destruct?t=g(a,o,i,s,function(){for(var d=[],v=0;v<arguments.length;v++)d[v]=arguments[v];return e(d)},function(){for(var d=[],v=0;v<arguments.length;v++)d[v]=arguments[v];return p(d)}):t=g(a,o,i,s,e,p),n=p});return t&&t.error&&(l.catch(function(){}),typeof n=="function"&&n(t.error)),l}function c2(a,o,i,s){return s===void 0&&(s={}),y(function(t,n){var l=g(a,o,i,s);l?l.error?n(l.error):l.then&&l.then(t).catch(n):n({error:"unexpected_error"})})}function e2(a,o,i,s){return s===void 0&&(s={}),new P(function(t){var n;return s.destruct?n=g(a,o,i,s,function(){for(var l=[],e=0;e<arguments.length;e++)l[e]=arguments[e];return t.next(l)},function(){for(var l=[],e=0;e<arguments.length;e++)l[e]=arguments[e];return t.error(l)}):n=g(a,o,i,s,t.next.bind(t),t.error.bind(t)),n&&n.error&&(t.error(n.error),t.complete()),function(){try{if(s.clearFunction)return s.clearWithArgs?g(a,s.clearFunction,i,s,t.next.bind(t),t.error.bind(t)):g(a,s.clearFunction,[])}catch(l){console.warn("Unable to clear the previous observable watch for",a.constructor.getPluginName(),o),console.warn(l)}}})}function r2(a,o){return o=typeof window<"u"&&o?e1(window,o):o||(typeof window<"u"?window:{}),D(o,a)}function h(a,o,i){var s,t,n;return typeof a=="string"?s=a:(s=a.constructor.getPluginRef(),i=a.constructor.getPluginName(),n=a.constructor.getPluginInstallName()),t=B(s),!t||o&&typeof t[o]>"u"?typeof window>"u"||!window.cordova?(w2(i,o),i2):(g2(i,n,o),n2):!0}function v2(a,o,i,s){if(o===void 0&&(o={}),o.sync)return a;if(o.callbackOrder==="reverse")a.unshift(s),a.unshift(i);else if(o.callbackStyle==="node")a.push(function(e,p){e?s(e):i(p)});else if(o.callbackStyle==="object"&&o.successName&&o.errorName){var t={};t[o.successName]=i,t[o.errorName]=s,a.push(t)}else if(typeof o.successIndex<"u"||typeof o.errorIndex<"u"){var n=function(){o.successIndex>a.length?a[o.successIndex]=i:a.splice(o.successIndex,0,i)},l=function(){o.errorIndex>a.length?a[o.errorIndex]=s:a.splice(o.errorIndex,0,s)};o.successIndex>o.errorIndex?(l(),n()):(n(),l())}else a.push(i),a.push(s);return a}function g(a,o,i,s,t,n){s===void 0&&(s={}),i=v2(i,s,t,n);var l=h(a,o);if(l===!0){var e=B(a.constructor.getPluginRef());return e[o].apply(e,i)}else return l}function B(a){return typeof window<"u"?e1(window,a):null}function e1(a,o){for(var i=o.split("."),s=a,t=0;t<i.length;t++){if(!s)return null;s=s[i[t]]}return s}function g2(a,o,i){console.warn(i?"Native: tried calling "+a+"."+i+", but the "+a+" plugin is not installed.":"Native: tried accessing the "+a+" plugin but it's not installed."),o&&console.warn("Install the "+a+" plugin: 'ionic cordova plugin add "+o+"'")}function w2(a,o){typeof process>"u"&&console.warn(o?"Native: tried calling "+a+"."+o+", but Cordova is not available. Make sure to include cordova.js or run in a device/simulator":"Native: tried accessing the "+a+" plugin but Cordova is not available. Make sure to include cordova.js or run in a device/simulator")}var S=function(a,o,i){return i===void 0&&(i={}),function(){for(var s=[],t=0;t<arguments.length;t++)s[t]=arguments[t];return i.sync?g(a,o,s,i):i.observable?e2(a,o,s,i):i.eventObservable&&i.event?r2(i.event,i.element):i.otherPromise?c2(a,o,s,i):l2(a,o,s,i)}};function r1(a,o){for(var i=o.split("."),s=a,t=0;t<i.length;t++){if(!s)return null;s=s[i[t]]}return s}var j=function(){function a(){}return a.installed=function(){var o=h(this.pluginRef)===!0;return o},a.getPlugin=function(){return typeof window<"u"?r1(window,this.pluginRef):null},a.getPluginName=function(){var o=this.pluginName;return o},a.getPluginRef=function(){var o=this.pluginRef;return o},a.getPluginInstallName=function(){var o=this.plugin;return o},a.getSupportedPlatforms=function(){var o=this.platforms;return o},a.pluginName="",a.pluginRef="",a.plugin="",a.repo="",a.platforms=[],a.install="",a}();function c(a,o,i,s){return S(a,o,i).apply(this,s)}c1();var L=function(a){F(o,a);function o(){return a!==null&&a.apply(this,arguments)||this}return o.prototype.enable=function(){return c(this,"enable",{sync:!0},arguments)},o.prototype.disable=function(){return c(this,"disable",{sync:!0},arguments)},o.prototype.setEnabled=function(i){return c(this,"setEnabled",{sync:!0},arguments)},o.prototype.fireEvent=function(i){for(var s=[],t=1;t<arguments.length;t++)s[t-1]=arguments[t];return c(this,"fireEvent",{sync:!0},arguments)},o.prototype.isEnabled=function(){return c(this,"isEnabled",{sync:!0},arguments)},o.prototype.isActive=function(){return c(this,"isActive",{sync:!0},arguments)},o.prototype.setDefaults=function(i){return c(this,"setDefaults",{platforms:["Android"]},arguments)},o.prototype.configure=function(i){return c(this,"configure",{platforms:["Android"],sync:!0},arguments)},o.prototype.on=function(i){return c(this,"on",{observable:!0,clearFunction:"un",clearWithArgs:!0},arguments)},o.prototype.un=function(i,s){return c(this,"un",{},arguments)},o.prototype.moveToBackground=function(){return c(this,"moveToBackground",{platforms:["Android"],sync:!0},arguments)},o.prototype.disableWebViewOptimizations=function(){return c(this,"disableWebViewOptimizations",{platforms:["Android"],sync:!0},arguments)},o.prototype.moveToForeground=function(){return c(this,"moveToForeground",{platforms:["Android"],sync:!0},arguments)},o.prototype.overrideBackButton=function(){return c(this,"overrideBackButton",{platforms:["Android"],sync:!0},arguments)},o.prototype.excludeFromTaskList=function(){return c(this,"excludeFromTaskList",{platforms:["Android"],sync:!0},arguments)},o.prototype.isScreenOff=function(i){return c(this,"isScreenOff",{platforms:["Android"]},arguments)},o.prototype.wakeUp=function(){return c(this,"wakeUp",{platforms:["Android"],sync:!0},arguments)},o.prototype.unlock=function(){return c(this,"unlock",{platforms:["Android"],sync:!0},arguments)},o.prototype.disableBatteryOptimizations=function(){return c(this,"disableBatteryOptimizations",{platforms:["Android"],sync:!0},arguments)},o.pluginName="BackgroundMode",o.plugin="cordova-plugin-background-mode",o.pluginRef="cordova.plugins.backgroundMode",o.repo="https://github.com/katzer/cordova-plugin-background-mode",o.platforms=["AmazonFire OS","Android","Browser","iOS","Windows"],o.decorators=[{type:T}],o}(j);var O,p2=function(){if(typeof window>"u")return new Map;if(!O){var a=window;a.Ionicons=a.Ionicons||{},O=a.Ionicons.map=a.Ionicons.map||new Map}return O},b=function(a){Object.keys(a).forEach(function(o){v1(o,a[o]);var i=o.replace(/([a-z0-9]|(?=[A-Z]))([A-Z0-9])/g,"$1-$2").toLowerCase();o!==i&&v1(i,a[o])})},v1=function(a,o){var i=p2(),s=i.get(a);s===void 0?i.set(a,o):s!==o&&console.warn('[Ionicons Warning]: Multiple icons were mapped to name "'.concat(a,'". Ensure that multiple icons are not mapped to the same icon name.'))};var g1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192 192-86 192-192z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M250.26 166.05L256 288l5.73-121.95a5.74 5.74 0 00-5.79-6h0a5.74 5.74 0 00-5.68 6z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 367.91a20 20 0 1120-20 20 20 0 01-20 20z'/></svg>";var w1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M268 112l144 144-144 144M392 256H100' class='ionicon-fill-none'/></svg>";var h1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M388 288a76 76 0 1076 76 76.24 76.24 0 00-76-76zM124 288a76 76 0 1076 76 76.24 76.24 0 00-76-76z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M256 360v-86l-64-42 80-88 40 72h56' class='ionicon-fill-none ionicon-stroke-width'/><path d='M320 136a31.89 31.89 0 0032-32.1A31.55 31.55 0 00320.2 72a32 32 0 10-.2 64z'/></svg>";var p1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M451 374c-15.88-16-54.34-39.35-73-48.76-24.3-12.24-26.3-13.24-45.4.95-12.74 9.47-21.21 17.93-36.12 14.75s-47.31-21.11-75.68-49.39-47.34-61.62-50.53-76.48 5.41-23.23 14.79-36c13.22-18 12.22-21 .92-45.3-8.81-18.9-32.84-57-48.9-72.8C119.9 44 119.9 47 108.83 51.6A160.15 160.15 0 0083 65.37C67 76 58.12 84.83 51.91 98.1s-9 44.38 23.07 102.64 54.57 88.05 101.14 134.49S258.5 406.64 310.85 436c64.76 36.27 89.6 29.2 102.91 23s22.18-15 32.83-31a159.09 159.09 0 0013.8-25.8C465 391.17 468 391.17 451 374z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var d1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M80 224l37.78-88.15C123.93 121.5 139.6 112 157.11 112h197.78c17.51 0 33.18 9.5 39.33 23.85L432 224M80 224h352v144H80zM112 368v32H80v-32M432 368v32h-32v-32' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='144' cy='288' r='16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='368' cy='288' r='16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var x1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M328 112L184 256l144 144' class='ionicon-fill-none'/></svg>";var m1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>",u1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M464 256c0-114.87-93.13-208-208-208S48 141.13 48 256s93.13 208 208 208 208-93.13 208-208zm-100.69-28.69l-96 96a16 16 0 01-22.62 0l-96-96a16 16 0 0122.62-22.62L256 289.37l84.69-84.68a16 16 0 0122.62 22.62z'/></svg>",M1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 64C150 64 64 150 64 256s86 192 192 192 192-86 192-192S362 64 256 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M352 216l-96 96-96-96' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var z1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 184l144 144 144-144' class='ionicon-fill-none'/></svg>";var f1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M112 328l144-144 144 144' class='ionicon-fill-none'/></svg>";var k1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm75.31 260.69a16 16 0 11-22.62 22.62L256 278.63l-52.69 52.68a16 16 0 01-22.62-22.62L233.37 256l-52.68-52.69a16 16 0 0122.62-22.62L256 233.37l52.69-52.68a16 16 0 0122.62 22.62L278.63 256z'/></svg>";var B1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M368 368L144 144M368 144L144 368' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var L1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 240c-8.89-89.54-71-144-144-144-69 0-113.44 48.2-128 96-60 6-112 43.59-112 112 0 66 54 112 120 112h260c55 0 100-27.44 100-88 0-59.82-53-85.76-96-88z' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var H1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M432 448a15.92 15.92 0 01-11.31-4.69l-352-352a16 16 0 0122.62-22.62l352 352A16 16 0 01432 448zM255.66 384c-41.49 0-81.5-12.28-118.92-36.5-34.07-22-64.74-53.51-88.7-91v-.08c19.94-28.57 41.78-52.73 65.24-72.21a2 2 0 00.14-2.94L93.5 161.38a2 2 0 00-2.71-.12c-24.92 21-48.05 46.76-69.08 76.92a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416a239.13 239.13 0 0075.8-12.58 2 2 0 00.77-3.31l-21.58-21.58a4 4 0 00-3.83-1 204.8 204.8 0 01-51.16 6.47zM490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96a227.34 227.34 0 00-74.89 12.83 2 2 0 00-.75 3.31l21.55 21.55a4 4 0 003.88 1 192.82 192.82 0 0150.21-6.69c40.69 0 80.58 12.43 118.55 37 34.71 22.4 65.74 53.88 89.76 91a.13.13 0 010 .16 310.72 310.72 0 01-64.12 72.73 2 2 0 00-.15 2.95l19.9 19.89a2 2 0 002.7.13 343.49 343.49 0 0068.64-78.48 32.2 32.2 0 00-.1-34.78z'/><path d='M256 160a95.88 95.88 0 00-21.37 2.4 2 2 0 00-1 3.38l112.59 112.56a2 2 0 003.38-1A96 96 0 00256 160zM165.78 233.66a2 2 0 00-3.38 1 96 96 0 00115 115 2 2 0 001-3.38z'/></svg>";var V1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M255.66 112c-77.94 0-157.89 45.11-220.83 135.33a16 16 0 00-.27 17.77C82.92 340.8 161.8 400 255.66 400c92.84 0 173.34-59.38 221.79-135.25a16.14 16.14 0 000-17.47C428.89 172.28 347.8 112 255.66 112z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='256' cy='256' r='80' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var C1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 80a176 176 0 10176 176A176 176 0 00256 80z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M200 202.29s.84-17.5 19.57-32.57C230.68 160.77 244 158.18 256 158c10.93-.14 20.69 1.67 26.53 4.45 10 4.76 29.47 16.38 29.47 41.09 0 26-17 37.81-36.37 50.8S251 281.43 251 296' stroke-linecap='round' stroke-miterlimit='10' stroke-width='28' class='ionicon-fill-none'/><circle cx='250' cy='348' r='20'/></svg>";var A1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M80 212v236a16 16 0 0016 16h96V328a24 24 0 0124-24h80a24 24 0 0124 24v136h96a16 16 0 0016-16V212' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M480 256L266.89 52c-5-5.28-16.69-5.34-21.78 0L32 256M400 179V64h-48v69' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var y1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184 184-82.39 184-184S349.61 64 248 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M220 220h32v116' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M208 340h88' class='ionicon-fill-none ionicon-stroke-width'/><path d='M248 130a26 26 0 1026 26 26 26 0 00-26-26z'/></svg>";var S1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M256 96V56M256 456v-40' class='ionicon-fill-none'/><path d='M256 112a144 144 0 10144 144 144 144 0 00-144-144z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M416 256h40M56 256h40' class='ionicon-fill-none'/></svg>",j1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='square' stroke-linejoin='round' stroke-width='48' d='M256 96V56M256 456v-40M256 112a144 144 0 10144 144 144 144 0 00-144-144zM416 256h40M56 256h40' class='ionicon-fill-none'/></svg>",O1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><circle cx='256' cy='192' r='32'/><path d='M256 32c-88.22 0-160 68.65-160 153 0 40.17 18.31 93.59 54.42 158.78 29 52.34 62.55 99.67 80 123.22a31.75 31.75 0 0051.22 0c17.42-23.55 51-70.88 80-123.22C397.69 278.61 416 225.19 416 185c0-84.35-71.78-153-160-153zm0 224a64 64 0 1164-64 64.07 64.07 0 01-64 64z'/></svg>",b1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48c-79.5 0-144 61.39-144 137 0 87 96 224.87 131.25 272.49a15.77 15.77 0 0025.5 0C304 409.89 400 272.07 400 185c0-75.61-64.5-137-144-137z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='256' cy='192' r='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var q1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M336 208v-95a80 80 0 00-160 0v95' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><rect x='96' y='208' width='320' height='272' rx='48' ry='48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var P1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><rect x='48' y='96' width='416' height='320' rx='40' ry='40' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M112 160l144 112 144-112' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var F1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M313.27 124.64L198.73 51.36a32 32 0 00-29.28.35L56.51 127.49A16 16 0 0048 141.63v295.8a16 16 0 0023.49 14.14l97.82-63.79a32 32 0 0129.5-.24l111.86 73a32 32 0 0029.27-.11l115.43-75.94a16 16 0 008.63-14.2V74.57a16 16 0 00-23.49-14.14l-98 63.86a32 32 0 01-29.24.35zM328 128v336M184 48v336' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var I1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-miterlimit='10' d='M80 160h352M80 256h352M80 352h352' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var D1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M272 464a16 16 0 01-16-16.42V264.13a8 8 0 00-8-8H64.41a16.31 16.31 0 01-15.49-10.65 16 16 0 018.41-19.87l384-176.15a16 16 0 0121.22 21.19l-176 384A16 16 0 01272 464z'/></svg>";var R1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M448 64L64 240.14h200a8 8 0 018 8V448z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var U1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M427.68 351.43C402 320 383.87 304 383.87 217.35 383.87 138 343.35 109.73 310 96c-4.43-1.82-8.6-6-9.95-10.55C294.2 65.54 277.8 48 256 48s-38.21 17.55-44 37.47c-1.35 4.6-5.52 8.71-9.95 10.53-33.39 13.75-73.87 41.92-73.87 121.35C128.13 304 110 320 84.32 351.43 73.68 364.45 83 384 101.61 384h308.88c18.51 0 27.77-19.61 17.19-32.57zM320 384v16a64 64 0 01-128 0v-16' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var T1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M402 168c-2.93 40.67-33.1 72-66 72s-63.12-31.32-66-72c-3-42.31 26.37-72 66-72s69 30.46 66 72z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M336 304c-65.17 0-127.84 32.37-143.54 95.41-2.08 8.34 3.15 16.59 11.72 16.59h263.65c8.57 0 13.77-8.25 11.72-16.59C463.85 335.36 401.18 304 336 304z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M200 185.94c-2.34 32.48-26.72 58.06-53 58.06s-50.7-25.57-53-58.06C91.61 152.15 115.34 128 147 128s55.39 24.77 53 57.94z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M206 306c-18.05-8.27-37.93-11.45-59-11.45-52 0-102.1 25.85-114.65 76.2-1.65 6.66 2.53 13.25 9.37 13.25H154' stroke-linecap='round' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var E1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M344 144c-3.92 52.87-44 96-88 96s-84.15-43.12-88-96c-4-55 35-96 88-96s92 42 88 96z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 304c-87 0-175.3 48-191.64 138.6C62.39 453.52 68.57 464 80 464h352c11.44 0 17.62-10.48 15.65-21.4C431.3 352 343 304 256 304z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var N1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M320 146s24.36-12-64-12a160 160 0 10160 160' stroke-linecap='round' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M256 58l80 80-80 80' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var W1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M456.69 421.39L362.6 327.3a173.81 173.81 0 0034.84-104.58C397.44 126.38 319.06 48 222.72 48S48 126.38 48 222.72s78.38 174.72 174.72 174.72A173.81 173.81 0 00327.3 362.6l94.09 94.09a25 25 0 0035.3-35.3zM97.92 222.72a124.8 124.8 0 11124.8 124.8 124.95 124.95 0 01-124.8-124.8z'/></svg>";var G1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M221.09 64a157.09 157.09 0 10157.09 157.09A157.1 157.1 0 00221.09 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M338.29 338.29L448 448' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var $1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M262.29 192.31a64 64 0 1057.4 57.4 64.13 64.13 0 00-57.4-57.4zM416.39 256a154.34 154.34 0 01-1.53 20.79l45.21 35.46a10.81 10.81 0 012.45 13.75l-42.77 74a10.81 10.81 0 01-13.14 4.59l-44.9-18.08a16.11 16.11 0 00-15.17 1.75A164.48 164.48 0 01325 400.8a15.94 15.94 0 00-8.82 12.14l-6.73 47.89a11.08 11.08 0 01-10.68 9.17h-85.54a11.11 11.11 0 01-10.69-8.87l-6.72-47.82a16.07 16.07 0 00-9-12.22 155.3 155.3 0 01-21.46-12.57 16 16 0 00-15.11-1.71l-44.89 18.07a10.81 10.81 0 01-13.14-4.58l-42.77-74a10.8 10.8 0 012.45-13.75l38.21-30a16.05 16.05 0 006-14.08c-.36-4.17-.58-8.33-.58-12.5s.21-8.27.58-12.35a16 16 0 00-6.07-13.94l-38.19-30A10.81 10.81 0 0149.48 186l42.77-74a10.81 10.81 0 0113.14-4.59l44.9 18.08a16.11 16.11 0 0015.17-1.75A164.48 164.48 0 01187 111.2a15.94 15.94 0 008.82-12.14l6.73-47.89A11.08 11.08 0 01213.23 42h85.54a11.11 11.11 0 0110.69 8.87l6.72 47.82a16.07 16.07 0 009 12.22 155.3 155.3 0 0121.46 12.57 16 16 0 0015.11 1.71l44.89-18.07a10.81 10.81 0 0113.14 4.58l42.77 74a10.8 10.8 0 01-2.45 13.75l-38.21 30a16.05 16.05 0 00-6.05 14.08c.33 4.14.55 8.3.55 12.47z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var X1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' d='M120 352l-24 48M136 432l-16 32M400 352l-24 48M416 432l-16 32M208 304l-16 96h48v80l80-112h-48l16-64M404.33 152.89H392.2C384.71 84.85 326.14 32 256 32a136.39 136.39 0 00-128.63 90.67h-4.57c-49.94 0-90.8 40.8-90.8 90.66h0C32 263.2 72.86 304 122.8 304h281.53C446 304 480 270 480 228.44h0c0-41.55-34-75.55-75.67-75.55z' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var Q1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 64C150 64 64 150 64 256s86 192 192 192 192-86 192-192S362 64 256 64z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-linejoin='round' d='M256 128v144h96' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var J1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M314.21 482.32l-56.77-114.74-44.89-57.39a72.82 72.82 0 01-10.13-37.05V144h15.67a40.22 40.22 0 0140.23 40.22v183.36M127.9 293.05v-74.52S165.16 144 202.42 144M370.1 274.42L304 231M170.53 478.36L224 400' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><circle cx='258.32' cy='69.48' r='37.26' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var Y1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M85.57 446.25h340.86a32 32 0 0028.17-47.17L284.18 82.58c-12.09-22.44-44.27-22.44-56.36 0L57.4 399.08a32 32 0 0028.17 47.17z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M250.26 195.39l5.74 122 5.73-121.95a5.74 5.74 0 00-5.79-6h0a5.74 5.74 0 00-5.68 5.95z' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M256 397.25a20 20 0 1120-20 20 20 0 01-20 20z'/></svg>";var Z1="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 320c0 88.37-55.63 144-144 144s-144-55.63-144-144c0-94.83 103.23-222.85 134.89-259.88a12 12 0 0118.23 0C296.77 97.15 400 225.17 400 320z' stroke-miterlimit='10' class='ionicon-fill-none ionicon-stroke-width'/><path d='M344 328a72 72 0 01-72 72' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";function H(){b({"thunderstorm-outline":X1,"water-outline":Z1,"cloud-outline":L1,"chevron-up":f1,"chevron-back-outline":x1,"chevron-down":m1,"chevron-down-circle":u1,"chevron-down-circle-outline":M1,"chevron-down-outline":z1,"arrow-forward-outline":w1,"walk-outline":J1,"bicycle-outline":h1,"car-outline":d1,"navigate-outline":R1,navigate:D1,"location-outline":b1,location:O1,"locate-outline":S1,locate:j1,"map-outline":F1,"time-outline":Q1,"information-circle-outline":y1,"help-circle-outline":C1,"alert-circle-outline":g1,"warning-outline":Y1,"call-outline":p1,"people-outline":T1,"person-outline":E1,"mail-outline":P1,"close-outline":B1,"close-circle":k1,"search-outline":G1,search:W1,"refresh-outline":N1,"menu-outline":I1,"settings-outline":$1,"home-outline":A1,"notifications-outline":U1,"lock-closed-outline":q1,"eye-outline":V1,"eye-off-outline":H1})}var K1=(()=>{let o=class o{constructor(s,t,n){this.platform=s,this.fcmService=t,this.backgroundMode=n;try{H()}catch(l){console.log("Error registering icons:",l)}this.initializeApp()}initializeApp(){this.platform.ready().then(()=>{try{console.log("App initialization started"),this.checkForNotificationInUrl(),this.initializeFCM(),console.log("App initialization completed successfully")}catch(s){console.error("Error during app initialization:",s)}}).catch(s=>{console.error("Error in platform.ready():",s)})}initializeFCM(){console.log("\u{1F525} Initializing FCM for all users (authenticated or not)"),this.fcmService.initPush().then(()=>{console.log("\u2705 FCM initialization completed")}).catch(s=>{console.error("\u274C FCM initialization failed:",s)})}checkForNotificationInUrl(){try{let s=new URL(window.location.href),t=s.searchParams.get("notification");if(t)try{let n=JSON.parse(decodeURIComponent(t));s.searchParams.delete("notification"),window.history.replaceState({},document.title,s.toString())}catch(n){console.error("Error parsing notification from URL:",n)}}catch(s){console.error("Error checking for notification in URL:",s)}}};o.\u0275fac=function(t){return new(t||o)(z(K),z(l1),z(L))},o.\u0275cmp=N({type:o,selectors:[["app-root"]],standalone:!1,decls:2,vars:0,template:function(t,n){t&1&&(W(0,"ion-app"),$(1,"ion-router-outlet"),G())},dependencies:[o1,a1],encapsulation:2});let a=o;return a})();var r=(a,o)=>localStorage.getItem("token")?!0:(M(k).navigate(["/login"]),!1);var _1=(a,o)=>localStorage.getItem("onboardingComplete")==="true"?!0:(M(k).navigate(["/welcome"]),!1);var x2=[{path:"",redirectTo:"loading",pathMatch:"full"},{path:"loading",loadComponent:()=>import("./chunk-ULXNU6DF.js").then(a=>a.LoadingPage)},{path:"login",loadComponent:()=>import("./chunk-HWKMFNZZ.js").then(a=>a.LoginPage)},{path:"register",loadComponent:()=>import("./chunk-TQVQ5UUZ.js").then(a=>a.RegisterPage)},{path:"environment-switcher",loadComponent:()=>import("./chunk-CFADIKQ5.js").then(a=>a.EnvironmentSwitcherPage)},{path:"network-diagnostics",loadComponent:()=>import("./chunk-3MDZNCZ2.js").then(a=>a.LoginDebugPage)},{path:"notification-test",loadComponent:()=>import("./chunk-PSCVL2TE.js").then(a=>a.NotificationTestPage)},{path:"welcome",loadComponent:()=>import("./chunk-RYHE2V26.js").then(a=>a.WelcomePage),canActivate:[r]},{path:"data",loadComponent:()=>import("./chunk-F7AYWZFE.js").then(a=>a.DataPage),canActivate:[r]},{path:"settings",loadComponent:()=>import("./chunk-5NQKNTNK.js").then(a=>a.SettingsPage),canActivate:[r]},{path:"notifications",loadComponent:()=>import("./chunk-4JJYZFMY.js").then(a=>a.NotificationsPage),canActivate:[r]},{path:"earthquake-map",loadComponent:()=>import("./chunk-Z2RGG7EE.js").then(a=>a.EarthquakeMapPage),canActivate:[r]},{path:"typhoon-map",loadComponent:()=>import("./chunk-O4R44HE4.js").then(a=>a.TyphoonMapPage),canActivate:[r]},{path:"flood-map",loadComponent:()=>import("./chunk-GPYKHURF.js").then(a=>a.FloodMapPage),canActivate:[r]},{path:"all-maps",loadComponent:()=>import("./chunk-YUWEPZU4.js").then(a=>a.AllMapsPage),canActivate:[r]},{path:"tabs",loadComponent:()=>import("./chunk-XURFAIWR.js").then(a=>a.TabsPage),canActivate:[r,_1],children:[{path:"home",loadComponent:()=>import("./chunk-7WTCZKBA.js").then(a=>a.HomePage)},{path:"search",loadComponent:()=>import("./chunk-DWXDKU4K.js").then(a=>a.SearchPage)},{path:"map",loadComponent:()=>import("./chunk-ZWWFZZ4G.js").then(a=>a.MapPage)},{path:"profile",loadComponent:()=>import("./chunk-LD3DFYNZ.js").then(a=>a.ProfilePage)},{path:"",redirectTo:"home",pathMatch:"full"}]}],o2=(()=>{let o=class o{};o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=f({type:o}),o.\u0275inj=m({imports:[A.forRoot(x2,{preloadingStrategy:Z}),A]});let a=o;return a})();var V=(()=>{let o=class o{constructor(s){this.alertCtrl=s}handleError(s){let t="An unknown error occurred";return s.error instanceof ErrorEvent?(t=`Error: ${s.error.message}`,console.error("Client-side error:",s.error.message)):(t=`Error Code: ${s.status}
Message: ${s.message}`,console.error(`Server-side error: Status: ${s.status}, Body: ${JSON.stringify(s.error)}`)),console.error("HTTP error:",s),I(()=>s)}showErrorAlert(s,t){return q(this,null,function*(){yield(yield this.alertCtrl.create({header:s,message:t,buttons:["OK"]})).present()})}handleAppError(s,t){console.error(`Error in ${t||"Unknown component"}:`,s),s&&s.isCritical&&this.showErrorAlert("Application Error","An unexpected error occurred. Please restart the application.")}};o.\u0275fac=function(t){return new(t||o)(u(s1))},o.\u0275prov=w({token:o,factory:o.\u0275fac,providedIn:"root"});let a=o;return a})();var a2=(()=>{let o=class o{constructor(s){this.errorHandlerService=s}intercept(s,t){return t.handle(s).pipe(U(1),R(n=>(console.log("HTTP Error Interceptor caught an error:",n),this.errorHandlerService.handleError(n))))}};o.\u0275fac=function(t){return new(t||o)(u(V))},o.\u0275prov=w({token:o,factory:o.\u0275fac});let a=o;return a})();var s2=(()=>{let o=class o{constructor(){}intercept(s,t){let n=localStorage.getItem("token");if(n){let l=s.clone({headers:s.headers.set("Authorization",`Bearer ${n}`)});return t.handle(l)}return t.handle(s)}};o.\u0275fac=function(t){return new(t||o)},o.\u0275prov=w({token:o,factory:o.\u0275fac});let a=o;return a})();var t2=(()=>{let o=class o{};o.\u0275fac=function(t){return new(t||o)},o.\u0275mod=f({type:o,bootstrap:[K1]}),o.\u0275inj=m({providers:[{provide:Y,useClass:_},{provide:E,useClass:V},{provide:C,useClass:s2,multi:!0},{provide:C,useClass:a2,multi:!0},n1,L],imports:[Q,t1.forRoot(),o2,J]});let a=o;return a})();H();i1.production&&void 0;X().bootstrapModule(t2).catch(a=>console.log(a));
