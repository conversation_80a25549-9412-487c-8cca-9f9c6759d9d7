import{a as le}from"./chunk-GF3ITUBX.js";import{a as re,b as se,c as me}from"./chunk-AC42TJRX.js";import{a as w}from"./chunk-KGX7B5OW.js";import{b as ae}from"./chunk-3KLEUTE3.js";import{a as oe}from"./chunk-FULEFYAM.js";import"./chunk-NETZAO6G.js";import{$ as T,$a as X,A as P,Ab as ie,C as v,Cb as L,D as m,Db as j,F as o,G as c,H as h,I as D,J as b,K as y,M as p,Ma as G,N as k,Na as R,O as A,Oa as J,Pa as K,Qa as W,U as V,W as I,Wa as Q,X as E,ab as B,ea as U,ib as Y,j as F,m as q,o as S,q as C,r as M,tb as Z,u as H,ub as ee,vb as te,y as u,z as g,zb as ne}from"./chunk-YFIZFQXH.js";import"./chunk-EFGZ4QZO.js";import"./chunk-VKNYRYKJ.js";import"./chunk-YLGV66M3.js";import"./chunk-VD4VFVTQ.js";import"./chunk-HC6MZPB3.js";import"./chunk-IP3IRUG3.js";import"./chunk-UKIOCGZG.js";import"./chunk-RMJ7PCZJ.js";import"./chunk-K54AU7WQ.js";import"./chunk-VBU7NMPV.js";import"./chunk-JIZJ6CAM.js";import"./chunk-MCRJI3T3.js";import"./chunk-6OW47QMA.js";import"./chunk-HBC3IQSD.js";import"./chunk-RXV5ZBOB.js";import"./chunk-ODNE7IRY.js";import"./chunk-F4H6ZFEG.js";import"./chunk-JWIEPCRG.js";import"./chunk-WTKF7BA6.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-L5T6STQ3.js";import"./chunk-3EJRMEWO.js";import"./chunk-GNOVVPTF.js";import"./chunk-BAKMWPBW.js";import"./chunk-5OMUW5VI.js";import"./chunk-OBXDPQ3V.js";import{a as $,b as z,f as he,h as d}from"./chunk-LNJ3S2LQ.js";var pe=he(me());var de=(()=>{let a=class a{constructor(e,t,n){this.http=e,this.offlineStorage=t,this.mapboxRouting=n}getRoute(e,t,n,i,s="walking"){return d(this,null,function*(){let l=yield this.offlineStorage.getRoute(e,t,n,i,s);if(l)return console.log("\u{1F4CD} Using cached route"),{coordinates:JSON.parse(l.route_data),distance:l.distance,duration:l.duration,isOffline:!0,travelMode:l.travel_mode};if(this.offlineStorage.isOnline()&&!this.offlineStorage.isOfflineMode())try{let f=yield this.getOnlineRoute(e,t,n,i,s);if(f)return yield this.cacheRoute(e,t,n,i,f,s),z($({},f),{isOffline:!1})}catch(f){console.warn("\u26A0\uFE0F Online routing failed, falling back to offline:",f)}return console.log("\u26A0\uFE0F Offline mode: No routing available. Show evacuation centers only."),null})}getOnlineRoute(e,t,n,i,s){return d(this,null,function*(){try{let l=yield this.mapboxRouting.getDirections(t,e,i,n,s);if(l.routes&&l.routes.length>0){let f=l.routes[0];return{coordinates:f.geometry.coordinates.map(x=>[x[1],x[0]]),distance:f.distance,duration:f.duration,isOffline:!1,travelMode:s}}}catch(l){console.error("\u274C Mapbox routing error:",l)}return null})}cacheRoute(e,t,n,i,s,l){return d(this,null,function*(){let f={start_lat:e,start_lng:t,end_lat:n,end_lng:i,disaster_type:"general",route_data:JSON.stringify(s.coordinates),distance:s.distance,duration:s.duration,travel_mode:l};yield this.offlineStorage.saveRoute(f)})}generateOfflineRoute(e,t,n,i,s){return console.log("\u26A0\uFE0F No routing available in offline mode - showing distance only"),null}calculateDistance(e,t,n,i){let l=this.toRadians(n-e),f=this.toRadians(i-t),_=Math.sin(l/2)*Math.sin(l/2)+Math.cos(this.toRadians(e))*Math.cos(this.toRadians(n))*Math.sin(f/2)*Math.sin(f/2);return 6371*(2*Math.atan2(Math.sqrt(_),Math.sqrt(1-_)))}toRadians(e){return e*(Math.PI/180)}estimateDuration(e,t){let n=e/1e3,i={walking:5,cycling:15,driving:40},s=i[t]||i.walking;return n/s*3600}addRouteToMap(e,t,n="#007bff",i=4){let s=pe.polyline(t.coordinates,{color:n,weight:i,opacity:t.isOffline?.7:1,dashArray:t.isOffline?"10, 5":void 0}).addTo(e),l=(t.distance/1e3).toFixed(1),f=Math.round(t.duration/60),_=t.isOffline?"Offline Route":"Online Route";return s.bindPopup(`
      <div class="route-popup">
        <strong>${_}</strong><br>
        Distance: ${l} km<br>
        Duration: ${f} min<br>
        Mode: ${t.travelMode}
      </div>
    `),s}preCacheRoutes(s,l,f){return d(this,arguments,function*(e,t,n,i=["walking","cycling"]){if(!this.offlineStorage.isOnline()){console.log("\u26A0\uFE0F Cannot pre-cache routes while offline");return}console.log("\u{1F504} Pre-caching routes for evacuation centers...");let _=0;for(let x of n)for(let ge of i)try{yield this.getRoute(e,t,x.latitude,x.longitude,ge),_++,yield new Promise(N=>setTimeout(N,500))}catch(N){console.warn(`Failed to cache route to ${x.name}:`,N)}console.log(`\u2705 Pre-cached ${_} routes`)})}};a.\u0275fac=function(t){return new(t||a)(S(T),S(w),S(se))},a.\u0275prov=q({token:a,factory:a.\u0275fac,providedIn:"root"});let r=a;return r})();function ye(r,a){if(r&1){let O=D();o(0,"ion-button",9),b("click",function(){C(O);let t=y();return M(t.enableOfflineMode())}),p(1," Continue Offline "),c()}}function we(r,a){if(r&1){let O=D();o(0,"ion-button",9),b("click",function(){C(O);let t=y();return M(t.syncData())}),h(1,"ion-icon",10),p(2," Sync "),c()}}function ve(r,a){if(r&1){let O=D();o(0,"ion-button",9),b("click",function(){C(O);let t=y();return M(t.prepareOfflineData())}),h(1,"ion-icon",11),p(2," Prepare "),c()}}function Se(r,a){if(r&1&&(o(0,"div",12),h(1,"ion-progress-bar",13),o(2,"div",14),p(3),c()()),r&2){let O=y();u(),m("value",O.preparationProgress),u(2),k(O.preparationStatus)}}var ue=(()=>{let a=class a{constructor(e,t,n,i,s,l){this.offlineStorage=e,this.offlineMap=t,this.offlineRouting=n,this.alertCtrl=i,this.loadingCtrl=s,this.toastCtrl=l,this.offlineModeEnabled=new H,this.dataSynced=new H,this.isOnline=navigator.onLine,this.isOfflineMode=!1,this.hasOfflineData=!1,this.isPreparingData=!1,this.preparationProgress=0,this.preparationStatus="",this.lastSyncTime=null}ngOnInit(){return d(this,null,function*(){this.onlineListener=()=>{this.isOnline=!0,this.checkDataStatus()},this.offlineListener=()=>{this.isOnline=!1,this.checkDataStatus()},window.addEventListener("online",this.onlineListener),window.addEventListener("offline",this.offlineListener),yield this.checkDataStatus()})}ngOnDestroy(){this.onlineListener&&window.removeEventListener("online",this.onlineListener),this.offlineListener&&window.removeEventListener("offline",this.offlineListener)}checkDataStatus(){return d(this,null,function*(){this.isOfflineMode=this.offlineStorage.isOfflineMode(),this.hasOfflineData=yield this.offlineStorage.isDataAvailable(),this.lastSyncTime=this.offlineStorage.getLastSyncTime()})}getBannerClass(){return this.isPreparingData?"preparing":this.isOnline?this.isOnline&&!this.hasOfflineData?"warning":"online":"offline"}getBannerIcon(){return this.isPreparingData?"download-outline":this.isOnline?this.isOnline&&!this.hasOfflineData?"warning-outline":"checkmark-circle-outline":"wifi-outline"}getBannerTitle(){return this.isPreparingData?"Preparing Offline Data":!this.isOnline&&this.hasOfflineData?"Offline Mode Available":!this.isOnline&&!this.hasOfflineData?"No Internet Connection":this.isOnline&&!this.hasOfflineData?"Offline Data Not Ready":"Connected & Ready"}getBannerSubtitle(){return this.isPreparingData?this.preparationStatus:!this.isOnline&&this.hasOfflineData?"Emergency data is available offline":!this.isOnline&&!this.hasOfflineData?"Limited functionality available":this.isOnline&&!this.hasOfflineData?"Prepare offline data for emergencies":this.lastSyncTime?`Last synced: ${new Date(this.lastSyncTime).toLocaleDateString()}`:"All systems operational"}showOfflineButton(){return!this.isOnline&&!this.isOfflineMode&&this.hasOfflineData}showSyncButton(){return this.isOnline&&this.hasOfflineData&&!this.isPreparingData}showPrepareButton(){return this.isOnline&&!this.hasOfflineData&&!this.isPreparingData}enableOfflineMode(){return d(this,null,function*(){yield(yield this.alertCtrl.create({header:"Enable Offline Mode",message:"Switch to offline mode to access cached evacuation data and maps?",buttons:[{text:"Cancel",role:"cancel"},{text:"Continue Offline",handler:()=>{this.offlineStorage.setOfflineMode(!0),this.isOfflineMode=!0,this.offlineModeEnabled.emit(),this.showToast("Offline mode enabled. Using cached data.","success")}}]})).present()})}syncData(){return d(this,null,function*(){let e=yield this.loadingCtrl.create({message:"Syncing evacuation data..."});yield e.present();try{let t=yield this.offlineStorage.syncEvacuationCenters();yield e.dismiss(),t?(this.dataSynced.emit(),this.checkDataStatus(),this.showToast("Data synced successfully","success")):this.showToast("Sync failed. Please try again.","danger")}catch{yield e.dismiss(),this.showToast("Sync error. Check your connection.","danger")}})}prepareOfflineData(){return d(this,null,function*(){yield(yield this.alertCtrl.create({header:"Prepare Offline Data",message:"Download evacuation centers and map data for offline use? This may take a few minutes and use mobile data.",buttons:[{text:"Cancel",role:"cancel"},{text:"Download",handler:()=>this.startDataPreparation()}]})).present()})}startDataPreparation(){return d(this,null,function*(){this.isPreparingData=!0,this.preparationProgress=0;try{this.preparationStatus="Downloading evacuation centers...";let e=yield this.offlineStorage.syncEvacuationCenters();if(this.preparationProgress=.3,!e)throw new Error("Failed to sync evacuation centers");this.preparationStatus="Getting your location...";let t=yield re.getCurrentPosition({enableHighAccuracy:!0,timeout:1e4});this.preparationProgress=.4;let n=t.coords.latitude,i=t.coords.longitude;this.preparationStatus="Downloading map tiles...",yield this.offlineMap.preloadMapTiles(n,i,25,(l,f)=>{let _=.4+l/f*.4;this.preparationProgress=_,this.preparationStatus=`Downloading map tiles... ${l}/${f}`}),this.preparationStatus="Pre-caching routes...";let s=yield this.offlineStorage.getEvacuationCenters();yield this.offlineRouting.preCacheRoutes(n,i,s.slice(0,10)),this.preparationProgress=1,this.preparationStatus="Preparation complete!",yield this.checkDataStatus(),setTimeout(()=>{this.isPreparingData=!1,this.showToast("Offline data prepared successfully!","success")},1e3)}catch(e){console.error("Data preparation failed:",e),this.isPreparingData=!1,this.showToast("Failed to prepare offline data. Please try again.","danger")}})}showToast(e,t){return d(this,null,function*(){yield(yield this.toastCtrl.create({message:e,duration:3e3,color:t,position:"bottom"})).present()})}};a.\u0275fac=function(t){return new(t||a)(g(w),g(le),g(de),g(ne),g(ie),g(L))},a.\u0275cmp=P({type:a,selectors:[["app-offline-banner"]],outputs:{offlineModeEnabled:"offlineModeEnabled",dataSynced:"dataSynced"},decls:13,vars:8,consts:[[1,"offline-banner",3,"ngClass"],[1,"banner-content"],[1,"banner-icon",3,"name"],[1,"banner-text"],[1,"banner-title"],[1,"banner-subtitle"],[1,"banner-actions"],["fill","clear","size","small","color","light",3,"click",4,"ngIf"],["class","preparation-progress",4,"ngIf"],["fill","clear","size","small","color","light",3,"click"],["name","sync-outline"],["name","download-outline"],[1,"preparation-progress"],[3,"value"],[1,"progress-text"]],template:function(t,n){t&1&&(o(0,"div",0)(1,"div",1),h(2,"ion-icon",2),o(3,"div",3)(4,"div",4),p(5),c(),o(6,"div",5),p(7),c()(),o(8,"div",6),v(9,ye,2,0,"ion-button",7)(10,we,3,0,"ion-button",7)(11,ve,3,0,"ion-button",7),c()(),v(12,Se,4,2,"div",8),c()),t&2&&(m("ngClass",n.getBannerClass()),u(2),m("name",n.getBannerIcon()),u(3),k(n.getBannerTitle()),u(2),k(n.getBannerSubtitle()),u(2),m("ngIf",n.showOfflineButton()),u(),m("ngIf",n.showSyncButton()),u(),m("ngIf",n.showPrepareButton()),u(),m("ngIf",n.isPreparingData))},dependencies:[j,R,B,Y,E,V,I],styles:[".offline-banner[_ngcontent-%COMP%]{padding:12px 16px;margin:8px 16px;border-radius:8px;transition:all .3s ease}.offline-banner.online[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745,#20c997);color:#fff}.offline-banner.offline[_ngcontent-%COMP%]{background:linear-gradient(135deg,#dc3545,#fd7e14);color:#fff}.offline-banner.preparing[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff,#6610f2);color:#fff}.offline-banner.warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffc107,#fd7e14);color:#212529}.banner-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.banner-icon[_ngcontent-%COMP%]{font-size:24px;flex-shrink:0}.banner-text[_ngcontent-%COMP%]{flex:1}.banner-title[_ngcontent-%COMP%]{font-weight:600;font-size:14px;margin-bottom:2px}.banner-subtitle[_ngcontent-%COMP%]{font-size:12px;opacity:.9}.banner-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.preparation-progress[_ngcontent-%COMP%]{margin-top:12px}.progress-text[_ngcontent-%COMP%]{font-size:12px;text-align:center;margin-top:4px;opacity:.9}ion-progress-bar[_ngcontent-%COMP%]{height:4px;border-radius:2px}"]});let r=a;return r})();function Ce(r,a){if(r&1&&(o(0,"ion-badge",23),p(1),c()),r&2){let O=y();u(),A(" ",O.unreadNotificationCount>99?"99+":O.unreadNotificationCount," ")}}var Ve=(()=>{let a=class a{constructor(e,t,n,i,s){this.router=e,this.toastCtrl=t,this.fcmService=n,this.http=i,this.offlineStorage=s,this.isOffline=!1,this.unreadNotificationCount=0,this.notificationSubscription=null,this.pollSubscription=null}ngOnInit(){let e=localStorage.getItem("isOffline");this.isOffline=e==="true",this.loadUnreadCount(),this.pollSubscription=F(3e4).subscribe(()=>{this.loadUnreadCount()}),this.notificationSubscription=this.fcmService.notifications$.subscribe(()=>{this.loadUnreadCount()})}ngOnDestroy(){this.notificationSubscription&&this.notificationSubscription.unsubscribe(),this.pollSubscription&&this.pollSubscription.unsubscribe()}toggleStatus(){this.isOffline=!this.isOffline,localStorage.setItem("isOffline",String(this.isOffline))}openDisasterMap(e){console.log(`\u{1F3E0} HOME: Opening disaster-specific map for: ${e}`);let t=e,n="";e==="earthquake"?(t="Earthquake",n="/earthquake-map"):e==="typhoon"?(t="Typhoon",n="/typhoon-map"):e==="flashflood"&&(t="Flash Flood",n="/flood-map"),console.log(`\u{1F3E0} HOME: Navigating to ${n} for ${t}`),this.toastCtrl.create({message:`\u{1F5FA}\uFE0F Opening ${t} evacuation centers...`,duration:2e3,color:"primary"}).then(i=>i.present()),this.router.navigate([n])}viewMap(){console.log("\u{1F3E0} HOME: Opening complete evacuation centers map"),this.toastCtrl.create({message:"\u{1F5FA}\uFE0F Opening complete evacuation centers map...",duration:2e3,color:"secondary"}).then(e=>e.present()),this.router.navigate(["/all-maps"])}loadUnreadCount(){return d(this,null,function*(){try{let e=yield this.http.get(`${oe.apiUrl}/notifications/unread-count`).toPromise();e&&(this.unreadNotificationCount=e.unread_count)}catch(e){console.error("Error loading unread notification count:",e)}})}openNotifications(){this.router.navigate(["/notifications"])}openDataDebug(){console.log("\u{1F41B} Opening data debug page"),this.router.navigate(["/data-debug"])}onOfflineModeEnabled(){console.log("\u{1F504} Offline mode enabled from banner"),this.isOffline=!0,this.showToast("Offline mode enabled. Using cached data.","success")}onDataSynced(){console.log("\u{1F504} Data synced from banner"),this.showToast("Evacuation data updated successfully","success")}showToast(e,t){return d(this,null,function*(){yield(yield this.toastCtrl.create({message:e,duration:3e3,color:t,position:"bottom"})).present()})}};a.\u0275fac=function(t){return new(t||a)(g(U),g(L),g(ae),g(T),g(w))},a.\u0275cmp=P({type:a,selectors:[["app-home"]],decls:43,vars:3,consts:[[3,"translucent"],["slot","end"],[1,"notification-button",3,"click"],["name","notifications-outline"],["class","notification-badge",4,"ngIf"],[3,"offlineModeEnabled","dataSynced"],[1,"ion-padding"],[1,"disaster-container"],[1,"home-title"],["src","assets/ALERTO.png","alt","App Logo",1,"home-logo"],[2,"font-size","22px"],[2,"font-size","35px","color","#1565c0","margin-top","0px"],[1,"top-disaster"],[1,"disaster","earthquake",3,"click"],["src","assets/earthquake.png","alt","Earthquake"],[1,"disaster","typhoon",3,"click"],["src","assets/typhoon.png","alt","Typhoon"],[1,"disaster","flood",3,"click"],["src","assets/flood.png","alt","Flood"],["expand","block",1,"view-map",2,"margin-top","24px","width","80%","height","45px","--border-radius","25px",3,"click","disabled"],["name","map","slot","start"],["expand","block","fill","outline","color","warning",2,"margin-top","12px","width","80%","height","40px","--border-radius","20px",3,"click"],["name","bug","slot","start"],[1,"notification-badge"]],template:function(t,n){t&1&&(o(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),p(3," Alerto "),c(),o(4,"ion-buttons",1)(5,"ion-button",2),b("click",function(){return n.openNotifications()}),h(6,"ion-icon",3),v(7,Ce,2,1,"ion-badge",4),c()()()(),o(8,"ion-content")(9,"app-offline-banner",5),b("offlineModeEnabled",function(){return n.onOfflineModeEnabled()})("dataSynced",function(){return n.onDataSynced()}),c(),o(10,"div",6)(11,"div",7)(12,"div",8),h(13,"img",9),o(14,"div",10),p(15,"Hi, Welcome to "),o(16,"p",11),p(17,"Safe Area!"),c()()(),o(18,"div",12)(19,"ion-card",13),b("click",function(){return n.openDisasterMap("earthquake")}),o(20,"ion-card-content"),h(21,"img",14),o(22,"ion-text")(23,"u"),p(24,"Earthquake"),c()()()(),o(25,"ion-card",15),b("click",function(){return n.openDisasterMap("typhoon")}),o(26,"ion-card-content"),h(27,"img",16),o(28,"ion-text")(29,"u"),p(30,"Typhoon"),c()()()(),o(31,"ion-card",17),b("click",function(){return n.openDisasterMap("flashflood")}),o(32,"ion-card-content"),h(33,"img",18),o(34,"ion-text")(35,"u"),p(36,"Flash Flood"),c()()()()(),o(37,"ion-button",19),b("click",function(){return n.viewMap()}),h(38,"ion-icon",20),p(39," See the Whole Map "),c(),o(40,"ion-button",21),b("click",function(){return n.openDataDebug()}),h(41,"ion-icon",22),p(42," Debug Data "),c()()()()),t&2&&(m("translucent",!0),u(7),m("ngIf",n.unreadNotificationCount>0),u(30),m("disabled",n.isOffline))},dependencies:[j,G,R,J,K,W,Q,X,B,Z,ee,te,E,I,ue],styles:[".status-text[_ngcontent-%COMP%]{margin-left:8px}ion-header[_ngcontent-%COMP%], ion-title[_ngcontent-%COMP%]{text-align:center;font-family:Poppins,Arial,sans-serif;font-size:2rem;font-weight:700;letter-spacing:1px;text-shadow:1px 2px 4px #ccc}.disaster-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:24px;margin:32px 0 0}.disaster[_ngcontent-%COMP%]{margin:0;cursor:pointer;transition:transform .2s}.disaster[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.disaster[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-align:center;padding:16px}.disaster[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:60px;height:60px;margin-bottom:8px}.earthquake[_ngcontent-%COMP%]{--background: #ffcc80}.typhoon[_ngcontent-%COMP%]{--background: #c5e1a5;size:100px;width:105px;height:120px}.flood[_ngcontent-%COMP%]{--background: #81d4fa}.view-map[_ngcontent-%COMP%]{margin-top:24px;--background: #00bfff}.view-map[_ngcontent-%COMP%]:hover{--background: #0090cc}.view-map[disabled][_ngcontent-%COMP%]{--background: #999}.top-disaster[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:16px;align-items:center}.home-logo[_ngcontent-%COMP%]{width:150px;height:150px}.home-title[_ngcontent-%COMP%]{padding-top:105px;display:flex;align-items:center;justify-content:center;font-size:30px;font-weight:700;letter-spacing:1px;text-shadow:1px 2px 4px #ccc}.notifications-section[_ngcontent-%COMP%]{margin-top:20px;border-top:1px solid var(--ion-color-light);padding-top:10px}ion-item-divider[_ngcontent-%COMP%]{--background: transparent;--color: var(--ion-color-primary);font-weight:700;font-size:1.1rem;letter-spacing:.5px;margin-bottom:8px}.notification-button[_ngcontent-%COMP%]{position:relative}.notification-badge[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;background:#e41e3f;color:#fff;font-size:10px;font-weight:600;min-width:16px;height:16px;border-radius:8px;display:flex;align-items:center;justify-content:center;z-index:10}"]});let r=a;return r})();export{Ve as HomePage};
